{% extends "base.html" %}

{% block title %}PYQs Admin | GPT Sir{% endblock %}

{% block head %}
<style>
  .pyqs-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .pyqs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e5e7eb;
  }

  .pyqs-header h1 {
    font-size: 1.875rem;
    font-weight: 700;
    color: #111827;
    margin: 0;
  }

  .create-btn {
    background-color: #10b981;
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .create-btn:hover {
    background-color: #059669;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  }

  .search-container {
    display: flex;
    margin-bottom: 24px;
    max-width: 400px;
  }

  .search-input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 8px 0 0 8px;
    font-size: 0.875rem;
    background-color: #f9fafb;
    transition: border-color 0.2s, background-color 0.2s;
  }

  .search-input:focus {
    outline: none;
    border-color: #10b981;
    background-color: white;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
  }

  .search-btn {
    background-color: #10b981;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 0 8px 8px 0;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.875rem;
    transition: background-color 0.2s;
  }

  .search-btn:hover {
    background-color: #059669;
  }

  .exams-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
  }

  .exam-item {
    background-color: white;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    overflow: hidden;
    transition: all 0.2s;
    position: relative;
  }

  .exam-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border-color: #d1d5db;
  }

  .exam-content {
    padding: 20px;
  }

  .exam-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
  }

  .exam-name {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #111827;
    line-height: 1.4;
    flex: 1;
    margin-right: 12px;
  }

  .exam-id {
    background-color: #f3f4f6;
    color: #6b7280;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
    white-space: nowrap;
  }

  .exam-meta {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-bottom: 20px;
  }

  .exam-meta-item {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .meta-label {
    font-size: 0.75rem;
    font-weight: 500;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.025em;
  }

  .meta-value {
    font-size: 0.875rem;
    color: #111827;
    font-weight: 500;
  }

  .exam-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .action-btn {
    padding: 8px 12px;
    border-radius: 6px;
    text-decoration: none;
    font-size: 0.75rem;
    font-weight: 600;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 4px;
    border: none;
    cursor: pointer;
    text-align: center;
    justify-content: center;
    flex: 1;
    min-width: 0;
  }

  .edit-btn {
    background-color: #f59e0b;
    color: white;
    border: 1px solid #f59e0b;
  }

  .edit-btn:hover {
    background-color: #d97706;
    border-color: #d97706;
  }

  .documents-btn {
    background-color: #06b6d4;
    color: white;
    border: 1px solid #06b6d4;
  }

  .documents-btn:hover {
    background-color: #0891b2;
    border-color: #0891b2;
  }

  .syllabus-btn {
    background-color: #8b5cf6;
    color: white;
    border: 1px solid #8b5cf6;
  }

  .syllabus-btn:hover {
    background-color: #7c3aed;
    border-color: #7c3aed;
  }

  .syllabus-btn:disabled {
    background-color: #e5e7eb;
    color: #9ca3af;
    border-color: #e5e7eb;
    cursor: not-allowed;
  }

  .view-json-btn {
    background-color: #3b82f6;
    color: white;
    border: 1px solid #3b82f6;
  }

  .view-json-btn:hover {
    background-color: #2563eb;
    border-color: #2563eb;
  }

  .view-json-btn:disabled {
    background-color: #e5e7eb;
    color: #9ca3af;
    border-color: #e5e7eb;
    cursor: not-allowed;
  }

  .loading {
    opacity: 0.6;
    pointer-events: none;
  }

  .spinner {
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #6b7280;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .pagination {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 32px;
  }

  .page-item {
    display: inline-block;
  }

  .page-link {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
    padding: 0 12px;
    border-radius: 8px;
    text-decoration: none;
    color: #6b7280;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.2s;
    border: 1px solid #e5e7eb;
    background-color: white;
  }

  .page-link:hover {
    background-color: #f9fafb;
    border-color: #d1d5db;
    color: #374151;
  }

  .page-item.active .page-link {
    background-color: #10b981;
    color: white;
    border-color: #10b981;
  }

  .page-item.disabled .page-link {
    color: #d1d5db;
    pointer-events: none;
    background-color: #f9fafb;
  }

  .no-exams {
    text-align: center;
    padding: 48px 24px;
    background-color: white;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
  }

  .no-exams p {
    margin-bottom: 20px;
    color: #6b7280;
    font-size: 1rem;
  }

  .no-exams a {
    color: #10b981;
    text-decoration: none;
    font-weight: 600;
  }

  .no-exams a:hover {
    text-decoration: underline;
  }

  .error-message {
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 0.875rem;
  }

  @media (max-width: 768px) {
    .exams-list {
      grid-template-columns: 1fr;
    }

    .pyqs-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }

    .search-container {
      max-width: none;
    }

    .exam-meta {
      grid-template-columns: 1fr;
    }

    .exam-actions {
      flex-direction: column;
    }

    .action-btn {
      flex: none;
    }
  }
</style>
{% endblock %}

{% block content %}
<div class="pyqs-container">
  <div class="pyqs-header">
    <h1>PYQs Admin</h1>
    <a href="/pyqs_admin/exams/create" class="create-btn">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
        <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
      </svg>
      Create New Exam
    </a>
  </div>

  <form action="/pyqs_admin/exams" method="get" class="search-container">
    <input type="text" name="search" placeholder="Search exams..." class="search-input" value="{{ search }}">
    <button type="submit" class="search-btn">Search</button>
  </form>

  {% if error %}
    <div class="error-message">
      {{ error }}
    </div>
  {% endif %}

  {% if exams %}
    <div class="exams-list">
      {% for exam in exams %}
        <div class="exam-item">
          <div class="exam-content">
            <div class="exam-header">
              <h3 class="exam-name">{{ exam.exam_name }}</h3>
              <div class="exam-id">ID: {{ exam.id }}</div>
            </div>

            <div class="exam-meta">
              <div class="exam-meta-item">
                <span class="meta-label">Level</span>
                <span class="meta-value">{{ exam.level }}</span>
              </div>
              <div class="exam-meta-item">
                <span class="meta-label">Grade</span>
                <span class="meta-value">{{ exam.grade }}</span>
              </div>
              <div class="exam-meta-item">
                <span class="meta-label">Syllabus</span>
                <span class="meta-value">{{ exam.syllabus }}</span>
              </div>
              <div class="exam-meta-item">
                <span class="meta-label">Subject</span>
                <span class="meta-value">{{ exam.subject }}</span>
              </div>
            </div>

            <div class="exam-actions">
              <a href="/pyqs_admin/exams/edit/{{ exam.id }}" class="action-btn edit-btn">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293l6.5-6.5zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325z"/>
                </svg>
                Edit
              </a>

              <a href="/pyqs_admin/exams/{{ exam.id }}/documents" class="action-btn documents-btn">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M14 14V4.5L9.5 0H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2zM9.5 3A1.5 1.5 0 0 0 11 4.5h2V14a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h5.5v2z"/>
                </svg>
                Documents
              </a>

              <button class="action-btn syllabus-btn" onclick="convertSyllabusToJson({{ exam.id }})" id="syllabus-btn-{{ exam.id }}">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M5 4a.5.5 0 0 0 0 1h6a.5.5 0 0 0 0-1H5zm-.5 2.5A.5.5 0 0 1 5 6h6a.5.5 0 0 1 0 1H5a.5.5 0 0 1-.5-.5zM5 8a.5.5 0 0 0 0 1h6a.5.5 0 0 0 0-1H5zm0 2a.5.5 0 0 0 0 1h3a.5.5 0 0 0 0-1H5z"/>
                  <path d="M2 2a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V2zm10-1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1z"/>
                </svg>
                Convert
              </button>

              <button class="action-btn view-json-btn" onclick="viewSyllabusJson({{ exam.id }})" id="view-json-btn-{{ exam.id }}" {% if not exam.syllabus_json %}disabled{% endif %}>
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M6 10.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5zm-2-3a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm-2-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5z"/>
                </svg>
                View JSON
              </button>
            </div>
          </div>
        </div>
      {% endfor %}
    </div>

    {% if pages > 1 %}
      <nav class="pagination">
        <div class="page-item {% if page == 1 %}disabled{% endif %}">
          <a class="page-link" href="/pyqs_admin/exams?page={{ page - 1 }}&limit={{ limit }}{% if search %}&search={{ search }}{% endif %}" aria-label="Previous">
            &laquo;
          </a>
        </div>

        {% for p in range(1, pages + 1) %}
          <div class="page-item {% if p == page %}active{% endif %}">
            <a class="page-link" href="/pyqs_admin/exams?page={{ p }}&limit={{ limit }}{% if search %}&search={{ search }}{% endif %}">{{ p }}</a>
          </div>
        {% endfor %}

        <div class="page-item {% if page == pages %}disabled{% endif %}">
          <a class="page-link" href="/pyqs_admin/exams?page={{ page + 1 }}&limit={{ limit }}{% if search %}&search={{ search }}{% endif %}" aria-label="Next">
            &raquo;
          </a>
        </div>
      </nav>
    {% endif %}
  {% else %}
    <div class="no-exams">
      <p>No exams found. {% if search %}Try a different search term or {% endif %}<a href="/pyqs_admin/exams/create">create your first exam</a>.</p>
    </div>
  {% endif %}
</div>

<script>
function convertSyllabusToJson(examId) {
    const button = document.getElementById(`syllabus-btn-${examId}`);
    const viewButton = document.getElementById(`view-json-btn-${examId}`);

    // Disable button and show loading
    button.disabled = true;
    button.innerHTML = '<span class="spinner"></span> Converting...';

    fetch(`/pyqs_admin/api/exams/${examId}/syllabus-to-json`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Syllabus successfully converted to JSON!');
            // Enable the view button
            viewButton.disabled = false;
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while converting syllabus to JSON');
    })
    .finally(() => {
        // Re-enable button and restore text
        button.disabled = false;
        button.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 16 16">
                <path d="M5 4a.5.5 0 0 0 0 1h6a.5.5 0 0 0 0-1H5zm-.5 2.5A.5.5 0 0 1 5 6h6a.5.5 0 0 1 0 1H5a.5.5 0 0 1-.5-.5zM5 8a.5.5 0 0 0 0 1h6a.5.5 0 0 0 0-1H5zm0 2a.5.5 0 0 0 0 1h3a.5.5 0 0 0 0-1H5z"/>
                <path d="M2 2a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V2zm10-1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1z"/>
            </svg>
            Convert
        `;
    });
}

function viewSyllabusJson(examId) {
    fetch(`/pyqs_admin/api/exams/${examId}/syllabus-json`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Create a modal to display the JSON
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background: white;
                padding: 24px;
                border-radius: 12px;
                max-width: 90%;
                max-height: 90%;
                overflow: auto;
                position: relative;
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            `;

            content.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; padding-bottom: 16px; border-bottom: 1px solid #e5e7eb;">
                    <h3 style="margin: 0; font-size: 1.25rem; font-weight: 600; color: #111827;">Syllabus JSON - Exam ID: ${examId}</h3>
                    <button onclick="this.closest('.modal').remove()" style="background: #f3f4f6; border: none; width: 32px; height: 32px; border-radius: 8px; cursor: pointer; display: flex; align-items: center; justify-content: center; color: #6b7280; font-size: 18px; transition: all 0.2s;" onmouseover="this.style.backgroundColor='#e5e7eb'" onmouseout="this.style.backgroundColor='#f3f4f6'">&times;</button>
                </div>
                <pre style="background: #f9fafb; padding: 16px; border-radius: 8px; overflow: auto; max-height: 500px; border: 1px solid #e5e7eb; font-size: 0.875rem; line-height: 1.5;">${JSON.stringify(JSON.parse(data.data.syllabus_json), null, 2)}</pre>
            `;

            modal.className = 'modal';
            modal.appendChild(content);
            document.body.appendChild(modal);

            // Close modal when clicking outside
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while fetching syllabus JSON');
    });
}
</script>
{% endblock %}
